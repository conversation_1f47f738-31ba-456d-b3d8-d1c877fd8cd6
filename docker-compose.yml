# docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_DB: sysposDB
      POSTGRES_USER: syspos
      POSTGRES_PASSWORD: adminsyspos
    volumes:
      - db_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  hasura:
    image: hasura/graphql-engine:v2.36.0
    restart: always
    depends_on:
      - postgres
    environment:
      HASURA_GRAPHQL_DATABASE_URL: *******************************************/sysposDB
      HASURA_GRAPHQL_ENABLE_CONSOLE: "true"
      HASURA_GRAPHQL_ADMIN_SECRET: adminsyspos # Clave secreta para la consola y tu backend
    ports:
      - "8080:8080"
      
volumes:
  db_data:
