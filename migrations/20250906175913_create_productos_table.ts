import type { Knex } from "knex";


export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('productos', (table) => {
    table.increments('id').primary();
    table.string('name').notNullable();
    table.string('codigo_barras').unique();
    table.string('codigo_referencia').unique();
    table.decimal('precio', 10, 2).notNullable();
    table.string('photo');
    table.timestamps(true, true);
  });
}


export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('productos');
}

