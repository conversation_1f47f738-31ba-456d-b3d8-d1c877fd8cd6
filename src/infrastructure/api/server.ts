import express, { Express } from "express";

let _server: any = null;

export function createApp(): Express {
  const app = express();
  app.use(express.json());

  app.get("/", (_req, res) => {
    res.json({ status: "ok", timestamp: new Date().toISOString() });
  });

  app.get("/health", (_req, res) => {
    res.send("ok");
  });

  return app;
}

export async function start(port?: number) {
  const p = port || (process.env.PORT ? Number(process.env.PORT) : 3000);
  const app = createApp();
  _server = app.listen(p, () => {
    // eslint-disable-next-line no-console
    console.log(`Server listening on http://localhost:${p}`);
  });
  return { app, server: _server };
}

export async function stop() {
  return new Promise<void>((resolve) => {
    if (!_server) return resolve();
    _server.close(() => {
      _server = null;
      resolve();
    });
  });
}

export default createApp();
