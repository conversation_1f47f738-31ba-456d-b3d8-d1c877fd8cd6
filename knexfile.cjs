// knexfile.cjs
require('ts-node/register');

module.exports = {
  development: {
    client: 'pg',
    connection: {
      host: 'localhost',
      port: 5432,
      user: 'syspos',
      password: 'adminsyspos',
      database: 'sysposDB'
    },
    migrations: {
      directory: './migrations',
      extension: 'ts',
      loadExtensions: ['.ts']
    },
    seeds: {
      directory: './seeds'
    }
  },

  production: {
    client: 'pg',
    connection: {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      user: process.env.DB_USER || 'syspos',
      password: process.env.DB_PASSWORD || 'adminsyspos',
      database: process.env.DB_NAME || 'sysposDB'
    },
    migrations: {
      directory: './migrations',
      extension: 'ts',
      loadExtensions: ['.ts']
    },
    seeds: {
      directory: './seeds'
    }
  }
};
